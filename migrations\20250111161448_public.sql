
create table "public"."products" (
    "id" bigint generated by default as identity not null,
    "created_at" timestamp with time zone not null default now(),
    "product_code" product_codes not null,
    "payment_link" text,
    "price_usd_pennies" bigint not null,
    "label" text not null
);

alter table "public"."products" enable row level security;

alter table "public"."payments" alter column "product_code" set data type product_codes using "product_code"::text::product_codes;

CREATE UNIQUE INDEX products_pkey ON public.products USING btree (id);

alter table "public"."products" add constraint "products_pkey" PRIMARY KEY using index "products_pkey";

grant delete on table "public"."products" to "anon";

grant insert on table "public"."products" to "anon";

grant references on table "public"."products" to "anon";

grant select on table "public"."products" to "anon";

grant trigger on table "public"."products" to "anon";

grant truncate on table "public"."products" to "anon";

grant update on table "public"."products" to "anon";

grant delete on table "public"."products" to "authenticated";

grant insert on table "public"."products" to "authenticated";

grant references on table "public"."products" to "authenticated";

grant select on table "public"."products" to "authenticated";

grant trigger on table "public"."products" to "authenticated";

grant truncate on table "public"."products" to "authenticated";

grant update on table "public"."products" to "authenticated";

grant delete on table "public"."products" to "service_role";

grant insert on table "public"."products" to "service_role";

grant references on table "public"."products" to "service_role";

grant select on table "public"."products" to "service_role";

grant trigger on table "public"."products" to "service_role";

grant truncate on table "public"."products" to "service_role";

grant update on table "public"."products" to "service_role";

create policy "Enable read access for all users"
on "public"."products"
as permissive
for select
to anon, authenticated
using (true);



