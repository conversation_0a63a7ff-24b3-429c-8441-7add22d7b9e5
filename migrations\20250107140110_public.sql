create type "public"."customer_job_status" as enum ('NEW', 'APPROVED', 'DECLINED', 'APPLIED', 'SATISFIED', 'DISSATISFIED');

create type "public"."customer_status" as enum ('PAID', 'NEW');

create type "public"."document_type" as enum ('CV', 'COVER', 'SCREENSHOT');

create type "public"."job_create_src" as enum ('AGENT');

create type "public"."job_status" as enum ('OPEN', 'CLOSED');

create type "public"."job_type" as enum ('FULLTIME', 'PARTTIME', 'CONTRACT', 'INTERNSHIP', 'TEMPORARY');
create type "public"."product_codes" as enum ('APPS_20', 'APPS_50', 'APPS_100', 'OTHER', 'APPS_500', 'NETWORK_80');

create type "public"."storage_source" as enum ('SUPABASE');

create table "public"."admins" (
    "id" bigint generated by default as identity not null,
    "created_at" timestamp with time zone not null default now(),
    "user_id" uuid not null default gen_random_uuid()
);


alter table "public"."admins" enable row level security;

create table "public"."agent_customers" (
    "created_at" timestamp with time zone not null default now(),
    "active" boolean not null default true,
    "customer_id" uuid not null default gen_random_uuid(),
    "agent_id" uuid not null default gen_random_uuid()
);


alter table "public"."agent_customers" enable row level security;

create table "public"."agents" (
    "created_at" timestamp with time zone not null default now(),
    "name" text not null,
    "email" text not null,
    "date_of_birth" date not null,
    "gender" text not null,
    "whatsapp_number" text not null,
    "country" text,
    "upwork_url" text,
    "linkedin_url" text,
    "active" boolean not null default true,
    "id" uuid not null default gen_random_uuid()
);


alter table "public"."agents" enable row level security;

create table "public"."customer_jobs" (
    "id" bigint generated by default as identity not null,
    "created_at" timestamp with time zone not null default now(),
    "status" customer_job_status not null default 'NEW'::customer_job_status,
    "customer_notes" text,
    "agent_notes" text,
    "customer_id" uuid not null default gen_random_uuid(),
    "job_id" uuid not null default gen_random_uuid(),
    "agent_id" uuid
);


alter table "public"."customer_jobs" enable row level security;

create table "public"."customers" (
    "created_at" timestamp with time zone not null default now(),
    "email" text not null,
    "status" customer_status not null default 'NEW'::customer_status,
    "pain_points" text,
    "other_pain_points" text,
    "whatsapp_number" text,
    "linkedin_url" text,
    "linkedin_premium" boolean,
    "university" text,
    "name" text not null,
    "whatsapp_group_url" text,
    "id" uuid not null default gen_random_uuid()
);


alter table "public"."customers" enable row level security;

create table "public"."documents" (
    "created_at" timestamp with time zone not null default now(),
    "source" storage_source not null default 'SUPABASE'::storage_source,
    "bucket" text not null,
    "path" text not null,
    "file_name" text not null,
    "document_type" document_type not null,
    "active" boolean not null default true,
    "id" uuid not null default gen_random_uuid(),
    "customer_id" uuid not null default gen_random_uuid()
);


alter table "public"."documents" enable row level security;

create table "public"."job_documents" (
    "id" bigint generated by default as identity not null,
    "created_at" timestamp with time zone not null default now(),
    "customer_job_id" bigint not null,
    "document_type" document_type not null default 'CV'::document_type,
    "customer_id" uuid not null default gen_random_uuid(),
    "document_id" uuid not null default gen_random_uuid()
);


alter table "public"."job_documents" enable row level security;

create table "public"."jobs" (
    "created_at" timestamp with time zone not null default now(),
    "url" text not null,
    "employer" text not null,
    "title" text not null,
    "location" text not null,
    "pay_amount" real,
    "pay_frequency" text,
    "pay_currency" text,
    "languages" text,
    "visa_required" text,
    "description" text not null,
    "create_src" job_create_src not null default 'AGENT'::job_create_src,
    "job_type" job_type not null default 'FULLTIME'::job_type,
    "job_status" job_status not null default 'OPEN'::job_status,
    "src_id" uuid,
    "id" uuid not null default gen_random_uuid()
);


alter table "public"."jobs" enable row level security;

create table "public"."payments" (
    "id" bigint generated by default as identity not null,
    "created_at" timestamp with time zone not null default now(),
    "event_id" text not null,
    "email" text not null,
    "amount_pennies" integer not null default 0,
    "product_code" product_codes not null,
    "name" text
);


alter table "public"."payments" enable row level security;

CREATE UNIQUE INDEX admins_pkey ON public.admins USING btree (id);

CREATE UNIQUE INDEX agent_customers_pkey ON public.agent_customers USING btree (customer_id);

CREATE UNIQUE INDEX agents_email_key ON public.agents USING btree (email);

CREATE UNIQUE INDEX agents_pkey ON public.agents USING btree (id);

CREATE UNIQUE INDEX customer_email_key ON public.customers USING btree (email);

CREATE UNIQUE INDEX customer_jobs_pkey ON public.customer_jobs USING btree (id);

CREATE UNIQUE INDEX customers_customer_id_key ON public.customers USING btree (id);

CREATE UNIQUE INDEX customers_pkey ON public.customers USING btree (id);

CREATE UNIQUE INDEX documents_pkey ON public.documents USING btree (id);

CREATE UNIQUE INDEX job_documents_pkey ON public.job_documents USING btree (id);

CREATE UNIQUE INDEX jobs_pkey ON public.jobs USING btree (id);

CREATE UNIQUE INDEX payments_event_id_key ON public.payments USING btree (event_id);

CREATE UNIQUE INDEX payments_pkey ON public.payments USING btree (id);

alter table "public"."admins" add constraint "admins_pkey" PRIMARY KEY using index "admins_pkey";

alter table "public"."agent_customers" add constraint "agent_customers_pkey" PRIMARY KEY using index "agent_customers_pkey";

alter table "public"."agents" add constraint "agents_pkey" PRIMARY KEY using index "agents_pkey";

alter table "public"."customer_jobs" add constraint "customer_jobs_pkey" PRIMARY KEY using index "customer_jobs_pkey";

alter table "public"."customers" add constraint "customers_pkey" PRIMARY KEY using index "customers_pkey";

alter table "public"."documents" add constraint "documents_pkey" PRIMARY KEY using index "documents_pkey";

alter table "public"."job_documents" add constraint "job_documents_pkey" PRIMARY KEY using index "job_documents_pkey";

alter table "public"."jobs" add constraint "jobs_pkey" PRIMARY KEY using index "jobs_pkey";

alter table "public"."payments" add constraint "payments_pkey" PRIMARY KEY using index "payments_pkey";

alter table "public"."admins" add constraint "admins_user_id_fkey" FOREIGN KEY (user_id) REFERENCES auth.users(id) not valid;

alter table "public"."admins" validate constraint "admins_user_id_fkey";

alter table "public"."agent_customers" add constraint "agent_customers_agent_id_fkey" FOREIGN KEY (agent_id) REFERENCES agents(id) not valid;

alter table "public"."agent_customers" validate constraint "agent_customers_agent_id_fkey";

alter table "public"."agent_customers" add constraint "agent_customers_customer_id_fkey" FOREIGN KEY (customer_id) REFERENCES customers(id) not valid;

alter table "public"."agent_customers" validate constraint "agent_customers_customer_id_fkey";

alter table "public"."agents" add constraint "agents_email_key" UNIQUE using index "agents_email_key";

alter table "public"."agents" add constraint "agents_id_fkey" FOREIGN KEY (id) REFERENCES auth.users(id) not valid;

alter table "public"."agents" validate constraint "agents_id_fkey";

alter table "public"."customer_jobs" add constraint "customer_jobs_agent_id_fkey" FOREIGN KEY (agent_id) REFERENCES agents(id) not valid;

alter table "public"."customer_jobs" validate constraint "customer_jobs_agent_id_fkey";

alter table "public"."customer_jobs" add constraint "customer_jobs_customer_id_fkey" FOREIGN KEY (customer_id) REFERENCES customers(id) not valid;

alter table "public"."customer_jobs" validate constraint "customer_jobs_customer_id_fkey";

alter table "public"."customer_jobs" add constraint "customer_jobs_job_id_fkey" FOREIGN KEY (job_id) REFERENCES jobs(id) not valid;

alter table "public"."customer_jobs" validate constraint "customer_jobs_job_id_fkey";

alter table "public"."customers" add constraint "customer_email_key" UNIQUE using index "customer_email_key";

alter table "public"."customers" add constraint "customers_customer_id_key" UNIQUE using index "customers_customer_id_key";

alter table "public"."customers" add constraint "customers_id_fkey" FOREIGN KEY (id) REFERENCES auth.users(id) not valid;

alter table "public"."customers" validate constraint "customers_id_fkey";

alter table "public"."documents" add constraint "documents_customer_id_fkey" FOREIGN KEY (customer_id) REFERENCES customers(id) not valid;

alter table "public"."documents" validate constraint "documents_customer_id_fkey";

alter table "public"."job_documents" add constraint "job_documents_customer_id_fkey" FOREIGN KEY (customer_id) REFERENCES customers(id) not valid;

alter table "public"."job_documents" validate constraint "job_documents_customer_id_fkey";

alter table "public"."job_documents" add constraint "job_documents_customer_job_id_fkey" FOREIGN KEY (customer_job_id) REFERENCES customer_jobs(id) not valid;

alter table "public"."job_documents" validate constraint "job_documents_customer_job_id_fkey";

alter table "public"."job_documents" add constraint "job_documents_document_id_fkey" FOREIGN KEY (document_id) REFERENCES documents(id) not valid;

alter table "public"."job_documents" validate constraint "job_documents_document_id_fkey";

alter table "public"."payments" add constraint "payments_event_id_key" UNIQUE using index "payments_event_id_key";

grant delete on table "public"."admins" to "authenticated";

grant insert on table "public"."admins" to "authenticated";

grant references on table "public"."admins" to "authenticated";

grant select on table "public"."admins" to "authenticated";

grant trigger on table "public"."admins" to "authenticated";

grant truncate on table "public"."admins" to "authenticated";

grant update on table "public"."admins" to "authenticated";

grant delete on table "public"."admins" to "service_role";

grant insert on table "public"."admins" to "service_role";

grant references on table "public"."admins" to "service_role";

grant select on table "public"."admins" to "service_role";

grant trigger on table "public"."admins" to "service_role";

grant truncate on table "public"."admins" to "service_role";

grant update on table "public"."admins" to "service_role";

grant delete on table "public"."agent_customers" to "authenticated";

grant insert on table "public"."agent_customers" to "authenticated";

grant references on table "public"."agent_customers" to "authenticated";

grant select on table "public"."agent_customers" to "authenticated";

grant trigger on table "public"."agent_customers" to "authenticated";

grant truncate on table "public"."agent_customers" to "authenticated";

grant update on table "public"."agent_customers" to "authenticated";

grant delete on table "public"."agent_customers" to "service_role";

grant insert on table "public"."agent_customers" to "service_role";

grant references on table "public"."agent_customers" to "service_role";

grant select on table "public"."agent_customers" to "service_role";

grant trigger on table "public"."agent_customers" to "service_role";

grant truncate on table "public"."agent_customers" to "service_role";

grant update on table "public"."agent_customers" to "service_role";

grant insert on table "public"."agents" to "anon";

grant delete on table "public"."agents" to "authenticated";

grant insert on table "public"."agents" to "authenticated";

grant references on table "public"."agents" to "authenticated";

grant select on table "public"."agents" to "authenticated";

grant trigger on table "public"."agents" to "authenticated";

grant truncate on table "public"."agents" to "authenticated";

grant update on table "public"."agents" to "authenticated";

grant delete on table "public"."agents" to "service_role";

grant insert on table "public"."agents" to "service_role";

grant references on table "public"."agents" to "service_role";

grant select on table "public"."agents" to "service_role";

grant trigger on table "public"."agents" to "service_role";

grant truncate on table "public"."agents" to "service_role";

grant update on table "public"."agents" to "service_role";

grant delete on table "public"."customer_jobs" to "authenticated";

grant insert on table "public"."customer_jobs" to "authenticated";

grant references on table "public"."customer_jobs" to "authenticated";

grant select on table "public"."customer_jobs" to "authenticated";

grant trigger on table "public"."customer_jobs" to "authenticated";

grant truncate on table "public"."customer_jobs" to "authenticated";

grant update on table "public"."customer_jobs" to "authenticated";

grant delete on table "public"."customer_jobs" to "service_role";

grant insert on table "public"."customer_jobs" to "service_role";

grant references on table "public"."customer_jobs" to "service_role";

grant select on table "public"."customer_jobs" to "service_role";

grant trigger on table "public"."customer_jobs" to "service_role";

grant truncate on table "public"."customer_jobs" to "service_role";

grant update on table "public"."customer_jobs" to "service_role";

grant insert on table "public"."customers" to "anon";

grant delete on table "public"."customers" to "authenticated";

grant insert on table "public"."customers" to "authenticated";

grant references on table "public"."customers" to "authenticated";

grant select on table "public"."customers" to "authenticated";

grant trigger on table "public"."customers" to "authenticated";

grant truncate on table "public"."customers" to "authenticated";

grant update on table "public"."customers" to "authenticated";

grant delete on table "public"."customers" to "service_role";

grant insert on table "public"."customers" to "service_role";

grant references on table "public"."customers" to "service_role";

grant select on table "public"."customers" to "service_role";

grant trigger on table "public"."customers" to "service_role";

grant truncate on table "public"."customers" to "service_role";

grant update on table "public"."customers" to "service_role";

grant delete on table "public"."documents" to "authenticated";

grant insert on table "public"."documents" to "authenticated";

grant references on table "public"."documents" to "authenticated";

grant select on table "public"."documents" to "authenticated";

grant trigger on table "public"."documents" to "authenticated";

grant truncate on table "public"."documents" to "authenticated";

grant update on table "public"."documents" to "authenticated";

grant delete on table "public"."documents" to "service_role";

grant insert on table "public"."documents" to "service_role";

grant references on table "public"."documents" to "service_role";

grant select on table "public"."documents" to "service_role";

grant trigger on table "public"."documents" to "service_role";

grant truncate on table "public"."documents" to "service_role";

grant update on table "public"."documents" to "service_role";

grant delete on table "public"."job_documents" to "authenticated";

grant insert on table "public"."job_documents" to "authenticated";

grant references on table "public"."job_documents" to "authenticated";

grant select on table "public"."job_documents" to "authenticated";

grant trigger on table "public"."job_documents" to "authenticated";

grant truncate on table "public"."job_documents" to "authenticated";

grant update on table "public"."job_documents" to "authenticated";

grant delete on table "public"."job_documents" to "service_role";

grant insert on table "public"."job_documents" to "service_role";

grant references on table "public"."job_documents" to "service_role";

grant select on table "public"."job_documents" to "service_role";

grant trigger on table "public"."job_documents" to "service_role";

grant truncate on table "public"."job_documents" to "service_role";

grant update on table "public"."job_documents" to "service_role";

grant delete on table "public"."jobs" to "authenticated";

grant insert on table "public"."jobs" to "authenticated";

grant references on table "public"."jobs" to "authenticated";

grant select on table "public"."jobs" to "authenticated";

grant trigger on table "public"."jobs" to "authenticated";

grant truncate on table "public"."jobs" to "authenticated";

grant update on table "public"."jobs" to "authenticated";

grant delete on table "public"."jobs" to "service_role";

grant insert on table "public"."jobs" to "service_role";

grant references on table "public"."jobs" to "service_role";

grant select on table "public"."jobs" to "service_role";

grant trigger on table "public"."jobs" to "service_role";

grant truncate on table "public"."jobs" to "service_role";

grant update on table "public"."jobs" to "service_role";

grant delete on table "public"."payments" to "authenticated";

grant insert on table "public"."payments" to "authenticated";

grant references on table "public"."payments" to "authenticated";

grant select on table "public"."payments" to "authenticated";

grant trigger on table "public"."payments" to "authenticated";

grant truncate on table "public"."payments" to "authenticated";

grant update on table "public"."payments" to "authenticated";

grant delete on table "public"."payments" to "service_role";

grant insert on table "public"."payments" to "service_role";

grant references on table "public"."payments" to "service_role";

grant select on table "public"."payments" to "service_role";

grant trigger on table "public"."payments" to "service_role";

grant truncate on table "public"."payments" to "service_role";

grant update on table "public"."payments" to "service_role";

create policy "Enable read access for authenticated users"
on "public"."admins"
as permissive
for select
to authenticated
using (true);


create policy "Enable insert for admins"
on "public"."agent_customers"
as permissive
for insert
to authenticated
with check ((( SELECT auth.uid() AS uid) IN ( SELECT admins.user_id
   FROM admins)));


create policy "Enable read access for authenticated users"
on "public"."agent_customers"
as permissive
for select
to authenticated
using (true);


create policy "Enable update for admins"
on "public"."agent_customers"
as permissive
for update
to authenticated
using ((( SELECT auth.uid() AS uid) IN ( SELECT admins.user_id
   FROM admins)));


create policy "Enable insert for anon"
on "public"."agents"
as permissive
for insert
to public
with check (true);


create policy "Enable read access for authenticated users"
on "public"."agents"
as permissive
for select
to authenticated
using (true);


create policy "Enable update for users based on email"
on "public"."agents"
as permissive
for update
to authenticated
using (((( SELECT auth.jwt() AS jwt) ->> 'email'::text) = email))
with check (((( SELECT auth.jwt() AS jwt) ->> 'email'::text) = email));


create policy "Enable insert for authenticated users only"
on "public"."customer_jobs"
as permissive
for insert
to authenticated
with check (true);


create policy "Enable read access for all users"
on "public"."customer_jobs"
as permissive
for select
to authenticated
using (true);


create policy "authenticated can update customer_jobs"
on "public"."customer_jobs"
as permissive
for update
to authenticated
using (true);


create policy "Enable insert for anon"
on "public"."customers"
as permissive
for insert
to anon, authenticated
with check (true);


create policy "Enable read access for all users"
on "public"."customers"
as permissive
for select
to authenticated
using (true);


create policy "Enable update for users based on email"
on "public"."customers"
as permissive
for update
to authenticated
using (((( SELECT auth.jwt() AS jwt) ->> 'email'::text) = email))
with check (((( SELECT auth.jwt() AS jwt) ->> 'email'::text) = email));


create policy " delete for authenticated users "
on "public"."documents"
as permissive
for delete
to authenticated
using (( SELECT true));


create policy "insert for authenticated users"
on "public"."documents"
as permissive
for insert
to authenticated
with check (true);


create policy "select for authenticated users"
on "public"."documents"
as permissive
for select
to authenticated
using (true);


create policy "update for authenticated users "
on "public"."documents"
as permissive
for update
to authenticated
using (true);


create policy "all for authenticated users"
on "public"."job_documents"
as permissive
for all
to authenticated
using (true)
with check (true);


create policy "Enable insert for authenticated users only"
on "public"."jobs"
as permissive
for insert
to authenticated
with check (true);


create policy "Enable read access for all users"
on "public"."jobs"
as permissive
for select
to authenticated
using (true);


create policy "Enable insert for authenticated users only"
on "public"."payments"
as permissive
for insert
to authenticated
with check (true);


create policy "Enable select for users based on email"
on "public"."payments"
as permissive
for select
to authenticated
using (((( SELECT auth.jwt() AS jwt) ->> 'email'::text) = email));



