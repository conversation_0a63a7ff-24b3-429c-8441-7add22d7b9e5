drop trigger if exists "cover_letter_updated_at" on "public"."cover_letters";

drop policy "Enable read access for authenticated users" on "public"."admins";

drop policy "Enable insert for admins" on "public"."agent_customers";

drop policy "Enable read access for authenticated users" on "public"."agent_customers";

drop policy "Enable update for admins" on "public"."agent_customers";

drop policy "Enable insert for anon" on "public"."agents";

drop policy "Enable read access for authenticated users" on "public"."agents";

drop policy "Enable update for users based on email" on "public"."agents";

drop policy "Enable all for authenticated users only" on "public"."contacts";

drop policy "Enable insert for authenticated" on "public"."cover_letters";

drop policy "Enable update for authenticated" on "public"."cover_letters";

drop policy "Select policy for authenticated" on "public"."cover_letters";

drop policy "Enable all for authenticated users only" on "public"."customer_contacts";

drop policy "Enable insert for authenticated users only" on "public"."customer_jobs";

drop policy "Enable read access for all users" on "public"."customer_jobs";

drop policy "authenticated can update customer_jobs" on "public"."customer_jobs";

drop policy "Customers can delete their own onboarding data" on "public"."customer_onboarding";

drop policy "Customers can insert their own onboarding data" on "public"."customer_onboarding";

drop policy "Customers can update their own onboarding data" on "public"."customer_onboarding";

drop policy "Enable read access for authenticated" on "public"."customer_onboarding";

drop policy "Enable insert for anon" on "public"."customers";

drop policy "Enable read access for all users" on "public"."customers";

drop policy "Enable update for authenticated" on "public"."customers";

drop policy "Enable update for users based on email" on "public"."customers";

drop policy " delete for authenticated users " on "public"."documents";

drop policy "insert for authenticated users" on "public"."documents";

drop policy "select for authenticated users" on "public"."documents";

drop policy "update for authenticated users " on "public"."documents";

drop policy "all for authenticated users" on "public"."job_documents";

drop policy "Enable insert for authenticated users only" on "public"."jobs";

drop policy "Enable read access for all users" on "public"."jobs";

drop policy "Update job for agents" on "public"."jobs";

drop policy "Enable insert for authenticated users" on "public"."nylas_customers";

drop policy "Enable read access for authenticated" on "public"."nylas_customers";

drop policy "Enable update for users based on email" on "public"."nylas_customers";

drop policy "Enable Delete for authenticated users only" on "public"."onboarding_locations";

drop policy "Enable insert for authenticated users only" on "public"."onboarding_locations";

drop policy "Enable read access for all users" on "public"."onboarding_locations";

drop policy "Enable update for authenticated users only" on "public"."onboarding_locations";

drop policy "Enable insert for service role" on "public"."payments";

drop policy "Enable read access for authenticated" on "public"."payments";

drop policy "Enable insert for service role" on "public"."plans";

drop policy "Enable read access for authanticated users" on "public"."plans";

drop policy "Enable read access for all users" on "public"."products";

revoke delete on table "public"."admins" from "anon";

revoke insert on table "public"."admins" from "anon";

revoke references on table "public"."admins" from "anon";

revoke select on table "public"."admins" from "anon";

revoke trigger on table "public"."admins" from "anon";

revoke truncate on table "public"."admins" from "anon";

revoke update on table "public"."admins" from "anon";

revoke delete on table "public"."admins" from "authenticated";

revoke insert on table "public"."admins" from "authenticated";

revoke references on table "public"."admins" from "authenticated";

revoke select on table "public"."admins" from "authenticated";

revoke trigger on table "public"."admins" from "authenticated";

revoke truncate on table "public"."admins" from "authenticated";

revoke update on table "public"."admins" from "authenticated";

revoke delete on table "public"."admins" from "service_role";

revoke insert on table "public"."admins" from "service_role";

revoke references on table "public"."admins" from "service_role";

revoke select on table "public"."admins" from "service_role";

revoke trigger on table "public"."admins" from "service_role";

revoke truncate on table "public"."admins" from "service_role";

revoke update on table "public"."admins" from "service_role";

revoke delete on table "public"."agent_customers" from "anon";

revoke insert on table "public"."agent_customers" from "anon";

revoke references on table "public"."agent_customers" from "anon";

revoke select on table "public"."agent_customers" from "anon";

revoke trigger on table "public"."agent_customers" from "anon";

revoke truncate on table "public"."agent_customers" from "anon";

revoke update on table "public"."agent_customers" from "anon";

revoke delete on table "public"."agent_customers" from "authenticated";

revoke insert on table "public"."agent_customers" from "authenticated";

revoke references on table "public"."agent_customers" from "authenticated";

revoke select on table "public"."agent_customers" from "authenticated";

revoke trigger on table "public"."agent_customers" from "authenticated";

revoke truncate on table "public"."agent_customers" from "authenticated";

revoke update on table "public"."agent_customers" from "authenticated";

revoke delete on table "public"."agent_customers" from "service_role";

revoke insert on table "public"."agent_customers" from "service_role";

revoke references on table "public"."agent_customers" from "service_role";

revoke select on table "public"."agent_customers" from "service_role";

revoke trigger on table "public"."agent_customers" from "service_role";

revoke truncate on table "public"."agent_customers" from "service_role";

revoke update on table "public"."agent_customers" from "service_role";

revoke delete on table "public"."agents" from "anon";

revoke insert on table "public"."agents" from "anon";

revoke references on table "public"."agents" from "anon";

revoke select on table "public"."agents" from "anon";

revoke trigger on table "public"."agents" from "anon";

revoke truncate on table "public"."agents" from "anon";

revoke update on table "public"."agents" from "anon";

revoke delete on table "public"."agents" from "authenticated";

revoke insert on table "public"."agents" from "authenticated";

revoke references on table "public"."agents" from "authenticated";

revoke select on table "public"."agents" from "authenticated";

revoke trigger on table "public"."agents" from "authenticated";

revoke truncate on table "public"."agents" from "authenticated";

revoke update on table "public"."agents" from "authenticated";

revoke delete on table "public"."agents" from "service_role";

revoke insert on table "public"."agents" from "service_role";

revoke references on table "public"."agents" from "service_role";

revoke select on table "public"."agents" from "service_role";

revoke trigger on table "public"."agents" from "service_role";

revoke truncate on table "public"."agents" from "service_role";

revoke update on table "public"."agents" from "service_role";

revoke delete on table "public"."contacts" from "anon";

revoke insert on table "public"."contacts" from "anon";

revoke references on table "public"."contacts" from "anon";

revoke select on table "public"."contacts" from "anon";

revoke trigger on table "public"."contacts" from "anon";

revoke truncate on table "public"."contacts" from "anon";

revoke update on table "public"."contacts" from "anon";

revoke delete on table "public"."contacts" from "authenticated";

revoke insert on table "public"."contacts" from "authenticated";

revoke references on table "public"."contacts" from "authenticated";

revoke select on table "public"."contacts" from "authenticated";

revoke trigger on table "public"."contacts" from "authenticated";

revoke truncate on table "public"."contacts" from "authenticated";

revoke update on table "public"."contacts" from "authenticated";

revoke delete on table "public"."contacts" from "service_role";

revoke insert on table "public"."contacts" from "service_role";

revoke references on table "public"."contacts" from "service_role";

revoke select on table "public"."contacts" from "service_role";

revoke trigger on table "public"."contacts" from "service_role";

revoke truncate on table "public"."contacts" from "service_role";

revoke update on table "public"."contacts" from "service_role";

revoke delete on table "public"."cover_letters" from "anon";

revoke insert on table "public"."cover_letters" from "anon";

revoke references on table "public"."cover_letters" from "anon";

revoke select on table "public"."cover_letters" from "anon";

revoke trigger on table "public"."cover_letters" from "anon";

revoke truncate on table "public"."cover_letters" from "anon";

revoke update on table "public"."cover_letters" from "anon";

revoke delete on table "public"."cover_letters" from "authenticated";

revoke insert on table "public"."cover_letters" from "authenticated";

revoke references on table "public"."cover_letters" from "authenticated";

revoke select on table "public"."cover_letters" from "authenticated";

revoke trigger on table "public"."cover_letters" from "authenticated";

revoke truncate on table "public"."cover_letters" from "authenticated";

revoke update on table "public"."cover_letters" from "authenticated";

revoke delete on table "public"."cover_letters" from "service_role";

revoke insert on table "public"."cover_letters" from "service_role";

revoke references on table "public"."cover_letters" from "service_role";

revoke select on table "public"."cover_letters" from "service_role";

revoke trigger on table "public"."cover_letters" from "service_role";

revoke truncate on table "public"."cover_letters" from "service_role";

revoke update on table "public"."cover_letters" from "service_role";

revoke delete on table "public"."customer_contacts" from "anon";

revoke insert on table "public"."customer_contacts" from "anon";

revoke references on table "public"."customer_contacts" from "anon";

revoke select on table "public"."customer_contacts" from "anon";

revoke trigger on table "public"."customer_contacts" from "anon";

revoke truncate on table "public"."customer_contacts" from "anon";

revoke update on table "public"."customer_contacts" from "anon";

revoke delete on table "public"."customer_contacts" from "authenticated";

revoke insert on table "public"."customer_contacts" from "authenticated";

revoke references on table "public"."customer_contacts" from "authenticated";

revoke select on table "public"."customer_contacts" from "authenticated";

revoke trigger on table "public"."customer_contacts" from "authenticated";

revoke truncate on table "public"."customer_contacts" from "authenticated";

revoke update on table "public"."customer_contacts" from "authenticated";

revoke delete on table "public"."customer_contacts" from "service_role";

revoke insert on table "public"."customer_contacts" from "service_role";

revoke references on table "public"."customer_contacts" from "service_role";

revoke select on table "public"."customer_contacts" from "service_role";

revoke trigger on table "public"."customer_contacts" from "service_role";

revoke truncate on table "public"."customer_contacts" from "service_role";

revoke update on table "public"."customer_contacts" from "service_role";

revoke delete on table "public"."customer_jobs" from "anon";

revoke insert on table "public"."customer_jobs" from "anon";

revoke references on table "public"."customer_jobs" from "anon";

revoke select on table "public"."customer_jobs" from "anon";

revoke trigger on table "public"."customer_jobs" from "anon";

revoke truncate on table "public"."customer_jobs" from "anon";

revoke update on table "public"."customer_jobs" from "anon";

revoke delete on table "public"."customer_jobs" from "authenticated";

revoke insert on table "public"."customer_jobs" from "authenticated";

revoke references on table "public"."customer_jobs" from "authenticated";

revoke select on table "public"."customer_jobs" from "authenticated";

revoke trigger on table "public"."customer_jobs" from "authenticated";

revoke truncate on table "public"."customer_jobs" from "authenticated";

revoke update on table "public"."customer_jobs" from "authenticated";

revoke delete on table "public"."customer_jobs" from "service_role";

revoke insert on table "public"."customer_jobs" from "service_role";

revoke references on table "public"."customer_jobs" from "service_role";

revoke select on table "public"."customer_jobs" from "service_role";

revoke trigger on table "public"."customer_jobs" from "service_role";

revoke truncate on table "public"."customer_jobs" from "service_role";

revoke update on table "public"."customer_jobs" from "service_role";

revoke delete on table "public"."customer_onboarding" from "anon";

revoke insert on table "public"."customer_onboarding" from "anon";

revoke references on table "public"."customer_onboarding" from "anon";

revoke select on table "public"."customer_onboarding" from "anon";

revoke trigger on table "public"."customer_onboarding" from "anon";

revoke truncate on table "public"."customer_onboarding" from "anon";

revoke update on table "public"."customer_onboarding" from "anon";

revoke delete on table "public"."customer_onboarding" from "authenticated";

revoke insert on table "public"."customer_onboarding" from "authenticated";

revoke references on table "public"."customer_onboarding" from "authenticated";

revoke select on table "public"."customer_onboarding" from "authenticated";

revoke trigger on table "public"."customer_onboarding" from "authenticated";

revoke truncate on table "public"."customer_onboarding" from "authenticated";

revoke update on table "public"."customer_onboarding" from "authenticated";

revoke delete on table "public"."customer_onboarding" from "service_role";

revoke insert on table "public"."customer_onboarding" from "service_role";

revoke references on table "public"."customer_onboarding" from "service_role";

revoke select on table "public"."customer_onboarding" from "service_role";

revoke trigger on table "public"."customer_onboarding" from "service_role";

revoke truncate on table "public"."customer_onboarding" from "service_role";

revoke update on table "public"."customer_onboarding" from "service_role";

revoke delete on table "public"."customers" from "anon";

revoke insert on table "public"."customers" from "anon";

revoke references on table "public"."customers" from "anon";

revoke select on table "public"."customers" from "anon";

revoke trigger on table "public"."customers" from "anon";

revoke truncate on table "public"."customers" from "anon";

revoke update on table "public"."customers" from "anon";

revoke delete on table "public"."customers" from "authenticated";

revoke insert on table "public"."customers" from "authenticated";

revoke references on table "public"."customers" from "authenticated";

revoke select on table "public"."customers" from "authenticated";

revoke trigger on table "public"."customers" from "authenticated";

revoke truncate on table "public"."customers" from "authenticated";

revoke update on table "public"."customers" from "authenticated";

revoke delete on table "public"."customers" from "service_role";

revoke insert on table "public"."customers" from "service_role";

revoke references on table "public"."customers" from "service_role";

revoke select on table "public"."customers" from "service_role";

revoke trigger on table "public"."customers" from "service_role";

revoke truncate on table "public"."customers" from "service_role";

revoke update on table "public"."customers" from "service_role";

revoke delete on table "public"."documents" from "anon";

revoke insert on table "public"."documents" from "anon";

revoke references on table "public"."documents" from "anon";

revoke select on table "public"."documents" from "anon";

revoke trigger on table "public"."documents" from "anon";

revoke truncate on table "public"."documents" from "anon";

revoke update on table "public"."documents" from "anon";

revoke delete on table "public"."documents" from "authenticated";

revoke insert on table "public"."documents" from "authenticated";

revoke references on table "public"."documents" from "authenticated";

revoke select on table "public"."documents" from "authenticated";

revoke trigger on table "public"."documents" from "authenticated";

revoke truncate on table "public"."documents" from "authenticated";

revoke update on table "public"."documents" from "authenticated";

revoke delete on table "public"."documents" from "service_role";

revoke insert on table "public"."documents" from "service_role";

revoke references on table "public"."documents" from "service_role";

revoke select on table "public"."documents" from "service_role";

revoke trigger on table "public"."documents" from "service_role";

revoke truncate on table "public"."documents" from "service_role";

revoke update on table "public"."documents" from "service_role";

revoke delete on table "public"."job_documents" from "anon";

revoke insert on table "public"."job_documents" from "anon";

revoke references on table "public"."job_documents" from "anon";

revoke select on table "public"."job_documents" from "anon";

revoke trigger on table "public"."job_documents" from "anon";

revoke truncate on table "public"."job_documents" from "anon";

revoke update on table "public"."job_documents" from "anon";

revoke delete on table "public"."job_documents" from "authenticated";

revoke insert on table "public"."job_documents" from "authenticated";

revoke references on table "public"."job_documents" from "authenticated";

revoke select on table "public"."job_documents" from "authenticated";

revoke trigger on table "public"."job_documents" from "authenticated";

revoke truncate on table "public"."job_documents" from "authenticated";

revoke update on table "public"."job_documents" from "authenticated";

revoke delete on table "public"."job_documents" from "service_role";

revoke insert on table "public"."job_documents" from "service_role";

revoke references on table "public"."job_documents" from "service_role";

revoke select on table "public"."job_documents" from "service_role";

revoke trigger on table "public"."job_documents" from "service_role";

revoke truncate on table "public"."job_documents" from "service_role";

revoke update on table "public"."job_documents" from "service_role";

revoke delete on table "public"."jobs" from "anon";

revoke insert on table "public"."jobs" from "anon";

revoke references on table "public"."jobs" from "anon";

revoke select on table "public"."jobs" from "anon";

revoke trigger on table "public"."jobs" from "anon";

revoke truncate on table "public"."jobs" from "anon";

revoke update on table "public"."jobs" from "anon";

revoke delete on table "public"."jobs" from "authenticated";

revoke insert on table "public"."jobs" from "authenticated";

revoke references on table "public"."jobs" from "authenticated";

revoke select on table "public"."jobs" from "authenticated";

revoke trigger on table "public"."jobs" from "authenticated";

revoke truncate on table "public"."jobs" from "authenticated";

revoke update on table "public"."jobs" from "authenticated";

revoke delete on table "public"."jobs" from "service_role";

revoke insert on table "public"."jobs" from "service_role";

revoke references on table "public"."jobs" from "service_role";

revoke select on table "public"."jobs" from "service_role";

revoke trigger on table "public"."jobs" from "service_role";

revoke truncate on table "public"."jobs" from "service_role";

revoke update on table "public"."jobs" from "service_role";

revoke delete on table "public"."nylas_customers" from "anon";

revoke insert on table "public"."nylas_customers" from "anon";

revoke references on table "public"."nylas_customers" from "anon";

revoke select on table "public"."nylas_customers" from "anon";

revoke trigger on table "public"."nylas_customers" from "anon";

revoke truncate on table "public"."nylas_customers" from "anon";

revoke update on table "public"."nylas_customers" from "anon";

revoke delete on table "public"."nylas_customers" from "authenticated";

revoke insert on table "public"."nylas_customers" from "authenticated";

revoke references on table "public"."nylas_customers" from "authenticated";

revoke select on table "public"."nylas_customers" from "authenticated";

revoke trigger on table "public"."nylas_customers" from "authenticated";

revoke truncate on table "public"."nylas_customers" from "authenticated";

revoke update on table "public"."nylas_customers" from "authenticated";

revoke delete on table "public"."nylas_customers" from "service_role";

revoke insert on table "public"."nylas_customers" from "service_role";

revoke references on table "public"."nylas_customers" from "service_role";

revoke select on table "public"."nylas_customers" from "service_role";

revoke trigger on table "public"."nylas_customers" from "service_role";

revoke truncate on table "public"."nylas_customers" from "service_role";

revoke update on table "public"."nylas_customers" from "service_role";

revoke delete on table "public"."onboarding_locations" from "anon";

revoke insert on table "public"."onboarding_locations" from "anon";

revoke references on table "public"."onboarding_locations" from "anon";

revoke select on table "public"."onboarding_locations" from "anon";

revoke trigger on table "public"."onboarding_locations" from "anon";

revoke truncate on table "public"."onboarding_locations" from "anon";

revoke update on table "public"."onboarding_locations" from "anon";

revoke delete on table "public"."onboarding_locations" from "authenticated";

revoke insert on table "public"."onboarding_locations" from "authenticated";

revoke references on table "public"."onboarding_locations" from "authenticated";

revoke select on table "public"."onboarding_locations" from "authenticated";

revoke trigger on table "public"."onboarding_locations" from "authenticated";

revoke truncate on table "public"."onboarding_locations" from "authenticated";

revoke update on table "public"."onboarding_locations" from "authenticated";

revoke delete on table "public"."onboarding_locations" from "service_role";

revoke insert on table "public"."onboarding_locations" from "service_role";

revoke references on table "public"."onboarding_locations" from "service_role";

revoke select on table "public"."onboarding_locations" from "service_role";

revoke trigger on table "public"."onboarding_locations" from "service_role";

revoke truncate on table "public"."onboarding_locations" from "service_role";

revoke update on table "public"."onboarding_locations" from "service_role";

revoke delete on table "public"."payments" from "anon";

revoke insert on table "public"."payments" from "anon";

revoke references on table "public"."payments" from "anon";

revoke select on table "public"."payments" from "anon";

revoke trigger on table "public"."payments" from "anon";

revoke truncate on table "public"."payments" from "anon";

revoke update on table "public"."payments" from "anon";

revoke delete on table "public"."payments" from "authenticated";

revoke insert on table "public"."payments" from "authenticated";

revoke references on table "public"."payments" from "authenticated";

revoke select on table "public"."payments" from "authenticated";

revoke trigger on table "public"."payments" from "authenticated";

revoke truncate on table "public"."payments" from "authenticated";

revoke update on table "public"."payments" from "authenticated";

revoke delete on table "public"."payments" from "service_role";

revoke insert on table "public"."payments" from "service_role";

revoke references on table "public"."payments" from "service_role";

revoke select on table "public"."payments" from "service_role";

revoke trigger on table "public"."payments" from "service_role";

revoke truncate on table "public"."payments" from "service_role";

revoke update on table "public"."payments" from "service_role";

revoke delete on table "public"."plans" from "anon";

revoke insert on table "public"."plans" from "anon";

revoke references on table "public"."plans" from "anon";

revoke select on table "public"."plans" from "anon";

revoke trigger on table "public"."plans" from "anon";

revoke truncate on table "public"."plans" from "anon";

revoke update on table "public"."plans" from "anon";

revoke delete on table "public"."plans" from "authenticated";

revoke insert on table "public"."plans" from "authenticated";

revoke references on table "public"."plans" from "authenticated";

revoke select on table "public"."plans" from "authenticated";

revoke trigger on table "public"."plans" from "authenticated";

revoke truncate on table "public"."plans" from "authenticated";

revoke update on table "public"."plans" from "authenticated";

revoke delete on table "public"."plans" from "service_role";

revoke insert on table "public"."plans" from "service_role";

revoke references on table "public"."plans" from "service_role";

revoke select on table "public"."plans" from "service_role";

revoke trigger on table "public"."plans" from "service_role";

revoke truncate on table "public"."plans" from "service_role";

revoke update on table "public"."plans" from "service_role";

revoke delete on table "public"."products" from "anon";

revoke insert on table "public"."products" from "anon";

revoke references on table "public"."products" from "anon";

revoke select on table "public"."products" from "anon";

revoke trigger on table "public"."products" from "anon";

revoke truncate on table "public"."products" from "anon";

revoke update on table "public"."products" from "anon";

revoke delete on table "public"."products" from "authenticated";

revoke insert on table "public"."products" from "authenticated";

revoke references on table "public"."products" from "authenticated";

revoke select on table "public"."products" from "authenticated";

revoke trigger on table "public"."products" from "authenticated";

revoke truncate on table "public"."products" from "authenticated";

revoke update on table "public"."products" from "authenticated";

revoke delete on table "public"."products" from "service_role";

revoke insert on table "public"."products" from "service_role";

revoke references on table "public"."products" from "service_role";

revoke select on table "public"."products" from "service_role";

revoke trigger on table "public"."products" from "service_role";

revoke truncate on table "public"."products" from "service_role";

revoke update on table "public"."products" from "service_role";

alter table "public"."admins" drop constraint "admins_user_id_fkey";

alter table "public"."agent_customers" drop constraint "agent_customers_agent_id_fkey";

alter table "public"."agent_customers" drop constraint "agent_customers_customer_id_fkey";

alter table "public"."agents" drop constraint "agents_email_key";

alter table "public"."agents" drop constraint "agents_id_fkey";

alter table "public"."contacts" drop constraint "contacts_email_key";

alter table "public"."cover_letters" drop constraint "cover_letters_customer_job_id_fkey";

alter table "public"."customer_contacts" drop constraint "customer_contacts_contact_id_fkey";

alter table "public"."customer_contacts" drop constraint "customer_contacts_customer_id_fkey";

alter table "public"."customer_jobs" drop constraint "customer_jobs_agent_id_fkey";

alter table "public"."customer_jobs" drop constraint "customer_jobs_customer_id_fkey";

alter table "public"."customer_jobs" drop constraint "customer_jobs_job_id_fkey";

alter table "public"."customer_onboarding" drop constraint "customer_onboarding_customer_id_fkey";

alter table "public"."customer_onboarding" drop constraint "customer_onboarding_customer_id_key";

alter table "public"."customers" drop constraint "customer_email_key";

alter table "public"."customers" drop constraint "customers_customer_id_key";

alter table "public"."customers" drop constraint "customers_id_fkey";

alter table "public"."documents" drop constraint "documents_customer_id_fkey";

alter table "public"."job_documents" drop constraint "job_documents_customer_id_fkey";

alter table "public"."job_documents" drop constraint "job_documents_customer_job_id_fkey";

alter table "public"."job_documents" drop constraint "job_documents_document_id_fkey";

alter table "public"."nylas_customers" drop constraint "nylas_customers_customer_id_fkey";

alter table "public"."nylas_customers" drop constraint "nylas_customers_customer_id_key";

alter table "public"."onboarding_locations" drop constraint "onboarding_locations_onboarding_id_fkey";

alter table "public"."payments" drop constraint "payments_event_id_key";

alter table "public"."plans" drop constraint "orders_customer_id_fkey";

alter table "public"."plans" drop constraint "orders_payment_id_fkey";

alter table "public"."plans" drop constraint "orders_payment_id_key";

alter table "public"."plans" drop constraint "orders_product_id_fkey";

alter table "public"."admins" drop constraint "admins_pkey";

alter table "public"."agent_customers" drop constraint "agent_customers_pkey";

alter table "public"."agents" drop constraint "agents_pkey";

alter table "public"."contacts" drop constraint "contacts_pkey";

alter table "public"."cover_letters" drop constraint "cover_letters_pkey";

alter table "public"."customer_contacts" drop constraint "customer_contacts_pkey";

alter table "public"."customer_jobs" drop constraint "customer_jobs_pkey";

alter table "public"."customer_onboarding" drop constraint "customer_onboarding_pkey";

alter table "public"."customers" drop constraint "customers_pkey";

alter table "public"."documents" drop constraint "documents_pkey";

alter table "public"."job_documents" drop constraint "job_documents_pkey";

alter table "public"."jobs" drop constraint "jobs_pkey";

alter table "public"."nylas_customers" drop constraint "nylas_customers_pkey";

alter table "public"."onboarding_locations" drop constraint "onboarding_locations_pkey";

alter table "public"."payments" drop constraint "payments_pkey";

alter table "public"."plans" drop constraint "Orders_pkey";

alter table "public"."products" drop constraint "products_pkey";

drop index if exists "public"."Orders_pkey";

drop index if exists "public"."admins_pkey";

drop index if exists "public"."agent_customers_pkey";

drop index if exists "public"."agents_email_key";

drop index if exists "public"."agents_pkey";

drop index if exists "public"."contacts_email_key";

drop index if exists "public"."contacts_pkey";

drop index if exists "public"."cover_letters_pkey";

drop index if exists "public"."customer_contacts_customer_id_contact_id_idx";

drop index if exists "public"."customer_contacts_pkey";

drop index if exists "public"."customer_email_key";

drop index if exists "public"."customer_jobs_pkey";

drop index if exists "public"."customer_onboarding_customer_id_key";

drop index if exists "public"."customer_onboarding_pkey";

drop index if exists "public"."customers_affiliate_id_idx";

drop index if exists "public"."customers_customer_id_key";

drop index if exists "public"."customers_pkey";

drop index if exists "public"."documents_pkey";

drop index if exists "public"."idx_customer_onboarding_customer_id";

drop index if exists "public"."job_documents_pkey";

drop index if exists "public"."jobs_pkey";

drop index if exists "public"."nylas_customers_customer_id_key";

drop index if exists "public"."nylas_customers_pkey";

drop index if exists "public"."onboarding_locations_pkey";

drop index if exists "public"."orders_payment_id_key";

drop index if exists "public"."payments_event_id_key";

drop index if exists "public"."payments_pkey";

drop index if exists "public"."products_pkey";

drop table "public"."admins";

drop table "public"."agent_customers";

drop table "public"."agents";

drop table "public"."contacts";

drop table "public"."cover_letters";

drop table "public"."customer_contacts";

drop table "public"."customer_jobs";

drop table "public"."customer_onboarding";

drop table "public"."customers";

drop table "public"."documents";

drop table "public"."job_documents";

drop table "public"."jobs";

drop table "public"."nylas_customers";

drop table "public"."onboarding_locations";

drop table "public"."payments";

drop table "public"."plans";

drop table "public"."products";

drop type "public"."contact_status";

drop type "public"."customer_job_status";

drop type "public"."customer_orientation_status";

drop type "public"."customer_status";

drop type "public"."document_type";

drop type "public"."job_create_src";

drop type "public"."job_status";

drop type "public"."job_type";

drop type "public"."nylas_status";

drop type "public"."order_status";

drop type "public"."product_codes";

drop type "public"."product_status";

drop type "public"."storage_source";

drop type "public"."workplace_type";


