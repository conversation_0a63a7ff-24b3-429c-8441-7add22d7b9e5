create table "public"."cover_letters" (
    "id" uuid not null default gen_random_uuid(),
    "created_at" timestamp with time zone not null default now(),
    "updated_at" timestamp without time zone,
    "customer_job_id" bigint not null,
    "data" text not null
);


alter table "public"."cover_letters" enable row level security;

CREATE UNIQUE INDEX cover_letters_pkey ON public.cover_letters USING btree (id);

alter table "public"."cover_letters" add constraint "cover_letters_pkey" PRIMARY KEY using index "cover_letters_pkey";

alter table "public"."cover_letters" add constraint "cover_letters_customer_job_id_fkey" FOREIGN KEY (customer_job_id) REFERENCES customer_jobs(id) not valid;

alter table "public"."cover_letters" validate constraint "cover_letters_customer_job_id_fkey";

grant delete on table "public"."cover_letters" to "anon";

grant insert on table "public"."cover_letters" to "anon";

grant references on table "public"."cover_letters" to "anon";

grant select on table "public"."cover_letters" to "anon";

grant trigger on table "public"."cover_letters" to "anon";

grant truncate on table "public"."cover_letters" to "anon";

grant update on table "public"."cover_letters" to "anon";

grant delete on table "public"."cover_letters" to "authenticated";

grant insert on table "public"."cover_letters" to "authenticated";

grant references on table "public"."cover_letters" to "authenticated";

grant select on table "public"."cover_letters" to "authenticated";

grant trigger on table "public"."cover_letters" to "authenticated";

grant truncate on table "public"."cover_letters" to "authenticated";

grant update on table "public"."cover_letters" to "authenticated";

grant delete on table "public"."cover_letters" to "service_role";

grant insert on table "public"."cover_letters" to "service_role";

grant references on table "public"."cover_letters" to "service_role";

grant select on table "public"."cover_letters" to "service_role";

grant trigger on table "public"."cover_letters" to "service_role";

grant truncate on table "public"."cover_letters" to "service_role";

grant update on table "public"."cover_letters" to "service_role";

create policy "Enable insert for authenticated"
on "public"."cover_letters"
as permissive
for insert
to authenticated
with check (true);


create policy "Enable update for authenticated"
on "public"."cover_letters"
as permissive
for update
to authenticated
using (true)
with check (true);


create policy "Select policy for authenticated"
on "public"."cover_letters"
as permissive
for select
to authenticated
using (true);



